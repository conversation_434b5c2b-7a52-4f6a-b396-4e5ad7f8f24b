/* 历史要素页面样式 */
.historical-element-page {
  .content-card {
    padding: 32px 24px;
    margin: 24px;
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
    background: white;
  }

  .page-title {
    text-align: center;
    margin-bottom: 16px;
    color: #333;
    font-size: 28px;
    font-weight: 600;
  }

  .page-description {
    text-align: center;
    font-size: 16px;
    margin-bottom: 40px;
    color: #666;
    line-height: 1.6;
  }

  .historical-card {
    border-radius: 12px !important;
    overflow: hidden !important;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08) !important;
    transition: all 0.3s ease !important;
    border: none !important;
    height: 100% !important;

    &:hover {
      transform: translateY(-4px) !important;
      box-shadow: 0 8px 24px rgba(0, 0, 0, 0.12) !important;
    }

    .historical-cover {
      height: 200px;
      background: linear-gradient(135deg, #ffd666 0%, #ff85c0 100%);
      display: flex;
      align-items: center;
      justify-content: center;
      position: relative;

      .historical-icon {
        width: 80px;
        height: 80px;
        background: rgba(255, 255, 255, 0.9);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        font-size: 32px;
        color: #ff7875;
      }
    }

    .ant-card-body {
      padding: 16px !important;
    }

    .card-header {
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 8px;

      .card-title {
        font-size: 16px;
        color: #333;
        font-weight: 500;
      }

      .card-code {
        font-size: 12px;
        background: #e6f4ff;
        color: #1677ff;
        padding: 2px 8px;
        border-radius: 4px;
        font-weight: 500;
      }
    }

    .card-info {
      margin-bottom: 6px;
      color: #666;
      font-size: 13px;

      .info-label {
        color: #999;
      }
    }

    .card-description {
      margin-bottom: 12px;
      color: #666;
      font-size: 13px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 2;
      overflow: hidden;
      line-height: 1.4;
    }

    .card-action {
      text-align: right;
      margin-top: auto;

      a {
        color: #1890ff;
        text-decoration: none;
        font-size: 13px;
        font-weight: 500;

        &:hover {
          color: #40a9ff;
        }
      }
    }
  }
}
