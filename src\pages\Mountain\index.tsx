import PublicLayout from '@/components/PublicLayout';
// import { history } from '@umijs/max';
import { Card, List, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { getPublicMountainList } from '@/services/mountain';
import { message } from 'antd';
import { history } from '@umijs/max';
import './index.less';

const { Title, Paragraph } = Typography;

const MountainPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.Mountain[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  const fetchList = async (p = page, ps = pageSize) => {
    setLoading(true);
    try {
      const res = await getPublicMountainList({ page: p, pageSize: ps });
      if (res.errCode === 0 && res.data) {
        setData(res.data.list || []);
        setTotal(res.data.total || 0);
        setPage(res.data.page || p);
        setPageSize(res.data.pageSize || ps);
      } else {
        message.error(res.msg || '加载失败');
      }
    } catch (e: any) {
      message.error(e?.message || '加载失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchList(1, 12);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // const handleDetailClick = (id: number) => {
  //   history.push(`/detail/mountain/${id}`);
  // };

  // const getRegionName = (regionId: number) => {
  //   // TODO: 从API获取区域名称
  //   console.log(regionId);
  //   return '未知区域';
  // };

  return (
    <PublicLayout>
      <div className="mountain-page">
        <div className="content-card">
          <Title level={2} className="page-title">
            关中地区山塬
          </Title>

          <Paragraph className="page-description">
            山塬是关中地区重要的地理要素，承载着深厚的历史文化内涵
          </Paragraph>

        <div className="list-container">
          <List
            loading={loading}
            grid={{ gutter: 24, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 3 }}
            dataSource={data}
            locale={{ emptyText: '暂无数据' }}
            pagination={{
              current: page,
              pageSize,
              total,
              onChange: (cp, cps) => fetchList(cp, cps),
              showSizeChanger: true,
            }}
            renderItem={(item) => (
              <List.Item>
                <Card
                  hoverable
                  style={{
                    borderRadius: '12px',
                    overflow: 'hidden',
                    boxShadow: '0 4px 12px rgba(0, 0, 0, 0.08)',
                    transition: 'all 0.3s ease',
                    border: 'none',
                    height: '100%'
                  }}
                  cover={
                    <div style={{
                      height: 200,
                      background: 'linear-gradient(135deg, #87e8de 0%, #52c41a 100%)',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      position: 'relative'
                    }}>
                      <div style={{
                        width: 80,
                        height: 80,
                        background: 'rgba(255, 255, 255, 0.9)',
                        borderRadius: '50%',
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        fontSize: '32px',
                        color: '#52c41a'
                      }}>
                        🏔️
                      </div>
                    </div>
                  }
                  bodyStyle={{ padding: '16px' }}
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', marginBottom: 8 }}>
                    <Typography.Text strong style={{ fontSize: 16, color: '#333' }}>{item.name}</Typography.Text>
                    <span style={{
                      fontSize: 12,
                      background: '#e6f4ff',
                      color: '#1677ff',
                      padding: '2px 8px',
                      borderRadius: 4,
                      fontWeight: 500
                    }}>
                      {item.code}
                    </span>
                  </div>
                  <div style={{ marginBottom: 6, color: '#666', fontSize: 13 }}>
                    <span style={{ color: '#999' }}>所属区域：</span>{item.regionDictId || '未知区域'}
                  </div>
                  <div style={{ marginBottom: 8, color: '#666', fontSize: 13 }}>
                    <span style={{ color: '#999' }}>海拔：</span>{item.height ? `${item.height}米` : '-'}
                  </div>
                  {item.historicalRecords && (
                    <div style={{
                      marginBottom: 12,
                      color: '#666',
                      fontSize: 13,
                      display: '-webkit-box',
                      WebkitBoxOrient: 'vertical',
                      WebkitLineClamp: 2,
                      overflow: 'hidden',
                      lineHeight: '1.4'
                    }}>
                      {item.historicalRecords}
                    </div>
                  )}
                  <div style={{ textAlign: 'right', marginTop: 'auto' }}>
                    <a
                      onClick={() => history.push(`/detail/mountain/${item.id}`)}
                      style={{
                        color: '#1890ff',
                        textDecoration: 'none',
                        fontSize: 13,
                        fontWeight: 500
                      }}
                    >
                      查看详情 →
                    </a>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        </div>
      </div>
    </PublicLayout>
  );
};

export default MountainPage;
