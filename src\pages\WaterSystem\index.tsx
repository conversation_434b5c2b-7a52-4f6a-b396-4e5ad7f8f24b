import PublicLayout from '@/components/PublicLayout';
// import { history } from '@umijs/max';
import { Card, List, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { getPublicWaterSystemList } from '@/services/waterSystem';
import { message } from 'antd';
import { history } from '@umijs/max';

const { Title, Paragraph } = Typography;

const WaterSystemPage: React.FC = () => {
  const [loading, setLoading] = useState(false);
  const [data, setData] = useState<API.WaterSystem[]>([]);
  const [total, setTotal] = useState(0);
  const [page, setPage] = useState(1);
  const [pageSize, setPageSize] = useState(12);

  const fetchList = async (p = page, ps = pageSize) => {
    setLoading(true);
    try {
      const res = await getPublicWaterSystemList({ page: p, pageSize: ps });
      if (res.errCode === 0 && res.data) {
        const list = (res.data as any).list || (res.data as any).data || [];
        setData(list);
        setTotal(res.data.total || 0);
        setPage(res.data.page || p);
        setPageSize(res.data.pageSize || ps);
      } else {
        message.error(res.msg || '加载失败');
      }
    } catch (e: any) {
      message.error(e?.message || '加载失败');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchList(1, 12);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);
  // const handleDetailClick = (id: number) => {
  //   history.push(`/detail/waterSystem/${id}`);
  // };

  // const getRegionName = (regionId: number) => {
  //   // TODO: 从API获取区域名称
  //   console.log('getRegionName', regionId);
  //   return '未知区域';
  // };

  return (
    <PublicLayout>
      <div className="content-card" style={{ padding: '24px' }}>
        <Title level={2} style={{ textAlign: 'center', marginBottom: 32 }}>
          关中地区水系
        </Title>

        <Paragraph
          style={{ textAlign: 'center', fontSize: 16, marginBottom: 32 }}
        >
          水系是关中地区的生命之源，滋养着这片古老的土地
        </Paragraph>

        <div className="list-container">
          <List
            loading={loading}
            grid={{ gutter: 24, xs: 1, sm: 2, md: 2, lg: 3, xl: 3, xxl: 3 }}
            dataSource={data}
            locale={{ emptyText: '暂无数据' }}
            pagination={{
              current: page,
              pageSize,
              total,
              onChange: (cp, cps) => fetchList(cp, cps),
              showSizeChanger: true,
            }}
            renderItem={(item) => (
              <List.Item>
                <Card
                  hoverable
                  cover={
                    <div style={{ height: 220, background: 'linear-gradient(135deg,#91d5ff 0%,#b37feb 100%)', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                      <img alt="water" src="/images/photos/photo2.jpg" style={{ width: 96, height: 96, objectFit: 'contain' }} onError={(e:any)=>{ e.currentTarget.style.display='none'; }} />
                    </div>
                  }
                >
                  <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Typography.Text strong style={{ fontSize: 16 }}>{item.name}</Typography.Text>
                    <span style={{ fontSize: 12, background: '#e6f4ff', color: '#1677ff', padding: '2px 8px', borderRadius: 6 }}>{item.code}</span>
                  </div>
                  <div style={{ marginTop: 8, color: '#666', fontSize: 13 }}>所属区域：{item.regionDictId}</div>
                  <div style={{ marginTop: 4, color: '#666', fontSize: 13 }}>长度/面积：{item.lengthArea ?? '-'}</div>
                  {item.historicalRecords && (
                    <div style={{ marginTop: 8, color: '#666', fontSize: 13, display: '-webkit-box', WebkitBoxOrient: 'vertical', WebkitLineClamp: 2, overflow: 'hidden' }}>{item.historicalRecords}</div>
                  )}
                  <div style={{ marginTop: 12, textAlign: 'right' }}>
                    <a onClick={() => history.push(`/detail/waterSystem/${item.id}`)}>查看详情</a>
                  </div>
                </Card>
              </List.Item>
            )}
          />
        </div>
      </div>
    </PublicLayout>
  );
};

export default WaterSystemPage;
