import PublicLayout from '@/components/PublicLayout';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useParams } from '@umijs/max';
import { Button, Card, Image, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { getPublicMountainDetail, getPublicMountainPhotos } from '@/services/mountain';
import { getPublicWaterSystemDetail, getPublicWaterSystemPhotos } from '@/services/waterSystem';
import { getPublicHistoricalElementDetail, getPublicHistoricalElementPhotos } from '@/services/historicalElement';
import { message } from 'antd';

const { Title } = Typography;

const DetailPage: React.FC = () => {
  const { type, id } = useParams();
  const numericId = Number(id);
  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [photos, setPhotos] = useState<Array<{ id: number; name: string; url: string }>>([]);

  const getTypeName = () => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'waterSystem':
        return '水系';
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  useEffect(() => {
    if (!type || !numericId) return;
    const load = async () => {
      setLoading(true);
      try {
        if (type === 'mountain') {
          const [d, p] = await Promise.all([
            getPublicMountainDetail(numericId),
            getPublicMountainPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'waterSystem') {
          const [d, p] = await Promise.all([
            getPublicWaterSystemDetail(numericId),
            getPublicWaterSystemPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'historicalElement') {
          const [d, p] = await Promise.all([
            getPublicHistoricalElementDetail(numericId),
            getPublicHistoricalElementPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        }
      } catch (e: any) {
        message.error(e?.message || '加载详情失败');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [type, numericId]);

  return (
    <PublicLayout>
      <div
        className="content-card"
        style={{
          padding: '24px',
          margin: '24px',
          borderRadius: '12px',
          boxShadow: '0 4px 12px rgba(0, 0, 0, 10%)'
        }}
      >
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => window.history.back()}
          style={{
            marginBottom: 24,
            borderRadius: '6px',
            border: '1px solid #d9d9d9'
          }}
        >
          返回
        </Button>

        <Title level={2} style={{
          marginBottom: 8,
          textAlign: 'center',
          color: '#333'
        }}>
          {detail?.name || getTypeName()}
        </Title>

        {detail?.code && (
          <div style={{
            textAlign: 'center',
            marginBottom: 24
          }}>
            <span style={{
              padding: '4px 12px',
              background: '#e6f4ff',
              color: '#1677ff',
              borderRadius: 6,
              fontSize: 14,
              fontWeight: 500
            }}>
              {detail.code}
            </span>
          </div>
        )}

        <div
          className="detail-layout"
          style={{
            display: 'grid',
            gridTemplateColumns: '1fr 1fr 320px',
            gap: 24,
            alignItems: 'start',
            marginTop: 24
          }}
        >
          <Card
            loading={loading}
            title="基本信息"
            bordered={false}
            style={{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 6%)'
            }}
            headStyle={{
              borderBottom: '1px solid #f0f0f0',
              fontSize: '16px',
              fontWeight: 600
            }}
          >
            <div style={{ lineHeight: '2', fontSize: '14px' }}>
              <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>名称：</span>
                <span style={{ color: '#333', fontWeight: 500 }}>{detail?.name || '-'}</span>
              </div>
              {type === 'mountain' && (
                <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                  <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>海拔高度：</span>
                  <span style={{ color: '#333' }}>{detail?.height ? `${detail.height}米` : '-'}</span>
                </div>
              )}
              {type === 'waterSystem' && (
                <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                  <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>长度/面积：</span>
                  <span style={{ color: '#333' }}>{detail?.lengthArea || '-'}</span>
                </div>
              )}
              {type === 'historicalElement' && (
                <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                  <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>建造时间：</span>
                  <span style={{ color: '#333' }}>{detail?.constructionTime || '-'}</span>
                </div>
              )}
              <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>所属区域：</span>
                <span style={{ color: '#333' }}>{detail?.regionDictId || '-'}</span>
              </div>
              <div style={{ marginBottom: 12, display: 'flex', alignItems: 'center' }}>
                <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>地理坐标：</span>
                <span style={{ color: '#333' }}>
                  {detail?.longitude || detail?.constructionLongitude || '-'}, {detail?.latitude || detail?.constructionLatitude || '-'}
                </span>
              </div>
              <div style={{ display: 'flex', alignItems: 'flex-start' }}>
                <span style={{ color: '#666', width: '80px', flexShrink: 0 }}>历史记载：</span>
                <span style={{ color: '#333', lineHeight: '1.6' }}>{detail?.historicalRecords || '-'}</span>
              </div>
            </div>
          </Card>

          <Card
            loading={loading}
            title="相关图片"
            bordered={false}
            style={{
              borderRadius: '8px',
              boxShadow: '0 2px 8px rgba(0, 0, 0, 6%)'
            }}
            headStyle={{
              borderBottom: '1px solid #f0f0f0',
              fontSize: '16px',
              fontWeight: 600
            }}
          >
            {photos?.length ? (
              <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                {photos.map((p) => (
                  <Image
                    key={p.id}
                    src={p.url}
                    style={{
                      width: '100%',
                      height: 180,
                      objectFit: 'cover',
                      borderRadius: '6px'
                    }}
                  />
                ))}
              </div>
            ) : (
              <div style={{
                color: '#999',
                textAlign: 'center',
                padding: '40px 0',
                background: '#fafafa',
                borderRadius: '6px'
              }}>
                暂无图片
              </div>
            )}
          </Card>

          <div>
            <Card
              title="关联信息"
              style={{
                marginBottom: 16,
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 6%)'
              }}
              bordered={false}
              headStyle={{
                borderBottom: '1px solid #f0f0f0',
                fontSize: '14px',
                fontWeight: 600
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ marginBottom: 8, fontWeight: 500, color: '#333' }}>选址关联</div>
              <div style={{ color: '#666', fontSize: '13px', lineHeight: '1.5' }}>与周边地理要素的空间关系</div>
            </Card>

            <Card
              title="历史沿革"
              style={{
                marginBottom: 16,
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 6%)'
              }}
              bordered={false}
              headStyle={{
                borderBottom: '1px solid #f0f0f0',
                fontSize: '14px',
                fontWeight: 600
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ color: '#666', fontSize: '13px', lineHeight: '1.5' }}>历史发展脉络和文化传承</div>
            </Card>

            <Card
              title="文化价值"
              bordered={false}
              style={{
                borderRadius: '8px',
                boxShadow: '0 2px 8px rgba(0, 0, 0, 6%)'
              }}
              headStyle={{
                borderBottom: '1px solid #f0f0f0',
                fontSize: '14px',
                fontWeight: 600
              }}
              bodyStyle={{ padding: '16px' }}
            >
              <div style={{ color: '#666', fontSize: '13px', lineHeight: '1.5' }}>承载的文化内涵和价值意义</div>
            </Card>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default DetailPage;
