import PublicLayout from '@/components/PublicLayout';
import { ArrowLeftOutlined } from '@ant-design/icons';
import { useParams } from '@umijs/max';
import { Button, Card, Image, Typography } from 'antd';
import React, { useEffect, useState } from 'react';
import { getPublicMountainDetail, getPublicMountainPhotos } from '@/services/mountain';
import { getPublicWaterSystemDetail, getPublicWaterSystemPhotos } from '@/services/waterSystem';
import { getPublicHistoricalElementDetail, getPublicHistoricalElementPhotos } from '@/services/historicalElement';
import { message } from 'antd';

const { Title } = Typography;

const DetailPage: React.FC = () => {
  const { type, id } = useParams();
  const numericId = Number(id);
  const [loading, setLoading] = useState<boolean>(false);
  const [detail, setDetail] = useState<any>(null);
  const [photos, setPhotos] = useState<Array<{ id: number; name: string; url: string }>>([]);

  const getTypeName = () => {
    switch (type) {
      case 'mountain':
        return '山塬';
      case 'waterSystem':
        return '水系';
      case 'historicalElement':
        return '历史要素';
      default:
        return '未知类型';
    }
  };

  useEffect(() => {
    if (!type || !numericId) return;
    const load = async () => {
      setLoading(true);
      try {
        if (type === 'mountain') {
          const [d, p] = await Promise.all([
            getPublicMountainDetail(numericId),
            getPublicMountainPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'waterSystem') {
          const [d, p] = await Promise.all([
            getPublicWaterSystemDetail(numericId),
            getPublicWaterSystemPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        } else if (type === 'historicalElement') {
          const [d, p] = await Promise.all([
            getPublicHistoricalElementDetail(numericId),
            getPublicHistoricalElementPhotos(numericId),
          ]);
          if (d.errCode === 0) setDetail(d.data || null);
          if (p.errCode === 0) setPhotos(p.data || []);
        }
      } catch (e: any) {
        message.error(e?.message || '加载详情失败');
      } finally {
        setLoading(false);
      }
    };
    load();
  }, [type, numericId]);

  return (
    <PublicLayout>
      <div
        className="content-card"
        style={{ padding: '24px', textAlign: 'center' }}
      >
        <Button
          icon={<ArrowLeftOutlined />}
          onClick={() => window.history.back()}
          style={{ marginBottom: 24 }}
        >
          返回
        </Button>
        <Title level={3} style={{ marginBottom: 8 }}>{detail?.name || getTypeName()}</Title>
        <div style={{ color: '#999', marginBottom: 16 }}>{detail?.code ? <span style={{ padding: '2px 8px', background: '#e6f4ff', color: '#1677ff', borderRadius: 6 }}>{detail.code}</span> : null}</div>

        <div style={{ display: 'grid', gridTemplateColumns: '1fr 1fr 320px', gap: 16, alignItems: 'start' }}>
          <Card loading={loading} title="基本信息" bordered={false}>
            <div style={{ marginBottom: 12 }}>名称：{detail?.name || '-'}</div>
            {type === 'mountain' && <div style={{ marginBottom: 12 }}>海拔高度：{detail?.height ?? '-'}米</div>}
            {type === 'waterSystem' && <div style={{ marginBottom: 12 }}>长度/面积：{detail?.lengthArea ?? '-'}</div>}
            {type === 'historicalElement' && <div style={{ marginBottom: 12 }}>建造时间：{detail?.constructionTime ?? '-'}</div>}
            <div style={{ marginBottom: 12 }}>所属区域：{detail?.regionDictId ?? '-'}</div>
            <div style={{ marginBottom: 12 }}>地理坐标：{detail?.longitude || detail?.constructionLongitude || '-'}, {detail?.latitude || detail?.constructionLatitude || '-'}</div>
            <div style={{ marginBottom: 12 }}>历史记载：{detail?.historicalRecords || '-'}</div>
          </Card>

          <Card loading={loading} title="相关图片" bordered={false}>
            {photos?.length ? (
              <div style={{ display: 'flex', flexDirection: 'column', gap: 12 }}>
                {photos.map((p) => (
                  <Image key={p.id} src={p.url} style={{ width: '100%', height: 180, objectFit: 'cover' }} />
                ))}
              </div>
            ) : (
              <div style={{ color: '#999' }}>暂无图片</div>
            )}
          </Card>

          <div>
            <Card title="关联系息" style={{ marginBottom: 16 }} bordered={false}>
              <div style={{ marginBottom: 8 }}>选址关联</div>
              <div style={{ color: '#999' }}>与周边地理要素的空间关系</div>
            </Card>
            <Card title="历史沿革" style={{ marginBottom: 16 }} bordered={false}>
              <div style={{ color: '#999' }}>历史发展脉络和文化传承</div>
            </Card>
            <Card title="文化价值" bordered={false}>
              <div style={{ color: '#999' }}>承载的文化内涵和价值意义</div>
            </Card>
          </div>
        </div>
      </div>
    </PublicLayout>
  );
};

export default DetailPage;
