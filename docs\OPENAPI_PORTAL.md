## 门户对接 API 文档（OpenAPI）

本文件基于现有后端实现与前端需求对齐，所有接口返回统一结构：

```json
{ "errCode": 0, "data": {}, "msg": "OK" }
```

- 基础路径: `/openapi`
- 时间: ISO 字符串
- 分页请求: `page`(默认1) `pageSize`(默认10)
- 分页响应: `list + total + page + pageSize`

---

### 1. 门户首页（Home）

1) 获取门户概览
- 路径: `GET /openapi/portal/overview`
- 查询: `regionId?: number`

2) 获取地图标记点
- 路径: `GET /openapi/portal/map-markers`
- 查询: `types?: mountain,waterSystem,historicalElement` `regionId?: number`

---

### 2. 山塬（列表/详情）

1) 列表（分页筛选）
- 路径: `GET /openapi/mountain/list`
- 查询: `page? pageSize? keyword? regionId?`

2) 详情
- 路径: `GET /openapi/mountain/{id}`

3) 详情-照片
- 路径: `GET /openapi/mountain/{id}/photos`

4) 下拉全量
- 路径: `GET /openapi/mountain/all`

---

### 3. 水系（列表/详情）

1) 列表（分页筛选）
- 路径: `GET /openapi/water-system/list`
- 查询: `page? pageSize? keyword? regionId?`

2) 详情
- 路径: `GET /openapi/water-system/{id}`

3) 详情-照片
- 路径: `GET /openapi/water-system/{id}/photos`

4) 下拉全量
- 路径: `GET /openapi/water-system/all`

---

### 4. 历史要素（列表/详情）

1) 列表（分页筛选）
- 路径: `GET /openapi/historical-element/list`
- 查询: `page? pageSize? keyword? regionId? typeId?`

2) 详情
- 路径: `GET /openapi/historical-element/{id}`

3) 详情-照片
- 路径: `GET /openapi/historical-element/{id}/photos`

4) 下拉全量
- 路径: `GET /openapi/historical-element/all`

---

### 5. 数字化统计（Digital）

1) 概览统计
- 路径: `GET /openapi/statistics/overview`
- 查询: `regionId?: number`

2) 时间轴
- 路径: `GET /openapi/statistics/timeline`
- 查询: `regionId?: number`

3) 增长趋势（可选）
- 路径: `GET /openapi/statistics/growth-trend`
- 查询: `period?: week|month|year`

---

### 6. 关系网络/相关推荐（公开）

1) 获取网络图数据
- 路径: `GET /openapi/relationship/network-graph`
- 查询: `sourceType? targetType? relationDictId? status?`

2) 根据要素获取关联关系列表
- 路径: `GET /openapi/relationship/by-element/{elementType}/{elementId}`

3) 关联统计
- 路径: `GET /openapi/relationship/statistics`

4) 搜索
- 路径: `GET /openapi/relationship/search?keyword=xxx`

---

### 7. 字典（门户筛选/展示）

1) 区域树
- 路径: `GET /openapi/region-dict/tree`

2) 类型树
- 路径: `GET /openapi/type-dict/tree`

3) 全量
- 路径: `GET /openapi/region-dict/all`
- 路径: `GET /openapi/type-dict/all`

---

### 8. 门户统一搜索（跨类型）
- 路径: `GET /openapi/search`
- 查询: `keyword` `type?: mountain|waterSystem|historicalElement|all` `page? pageSize?`

---

### 9. 字段说明（关键字段）
- 公共：`id`、`name`、`code`、`regionDictId`、`typeDictId`、`photos[{id,name,url}]`、`createdAt/updatedAt`
- 山塬：`height`、`longitude/latitude`
- 水系：`lengthArea`、`longitude/latitude`
- 历史要素：`constructionTime`、`constructionLongitude/constructionLatitude`、`historicalRecords`
- 时间轴：`year`、`elements[{id,name,type}]`
- 网络图：`nodes[{id,name,type,category,size,color}]`、`links[{source,target,relation,direction?,term?,weight,color}]`


这是后台实现后的接口，请按实际情况进行实现。


