import {
  getWaterSystemList,
  getWaterSystemStatistics,
} from '@/services/waterSystem';
import { message } from 'antd';
import { useCallback, useState } from 'react';

export interface UseWaterSystemDataReturn {
  // 数据状态
  data: API.WaterSystem[];
  statistics: API.WaterSystemStatistics | null;

  // 加载状态
  loading: boolean;
  statisticsLoading: boolean;

  // 分页状态
  pagination: {
    current: number;
    pageSize: number;
    total: number;
  };

  // 数据获取函数
  fetchData: (
    page?: number,
    pageSize?: number,
    keyword?: string,
    regionId?: number,
    typeId?: number,
  ) => Promise<void>;
  fetchStatistics: (regionId?: number) => Promise<void>;

  // 状态更新函数
  setPagination: React.Dispatch<
    React.SetStateAction<{
      current: number;
      pageSize: number;
      total: number;
    }>
  >;
}

export const useWaterSystemData = (): UseWaterSystemDataReturn => {
  // 数据状态
  const [data, setData] = useState<API.WaterSystem[]>([]);
  const [statistics, setStatistics] =
    useState<API.WaterSystemStatistics | null>(null);

  // 加载状态
  const [loading, setLoading] = useState(false);
  const [statisticsLoading, setStatisticsLoading] = useState(false);

  // 分页状态
  const [pagination, setPagination] = useState({
    current: 1,
    pageSize: 10,
    total: 0,
  });

  // 获取水系列表数据
  const fetchData = useCallback(
    async (
      page = 1,
      pageSize = 10,
      keyword?: string,
      regionId?: number,
      typeId?: number,
    ) => {
      setLoading(true);
      try {
        const params: API.GetWaterSystemListParams = {
          page,
          pageSize,
          keyword,
          regionId,
          typeId,
        };

        const response = await getWaterSystemList(params);

        if (response.errCode === 0) {
          setData(response.data?.list || []);
          setPagination({
            current: response.data?.page || page,
            pageSize: response.data?.pageSize || pageSize,
            total: response.data?.total || 0,
          });
        } else {
          message.error(response.msg || '获取水系列表失败');
          setData([]);
          setPagination({ current: 1, pageSize: 10, total: 0 });
        }
      } catch (error: any) {
        console.error('获取水系列表失败:', error);
        message.error('获取水系列表失败');
        setData([]);
        setPagination({ current: 1, pageSize: 10, total: 0 });
      } finally {
        setLoading(false);
      }
    },
    [],
  );

  // 获取统计数据
  const fetchStatistics = useCallback(async (regionId?: number) => {
    setStatisticsLoading(true);
    try {
      const params = {
        regionId,
      };

      const response = await getWaterSystemStatistics(params);

      if (response.errCode === 0) {
        setStatistics(response.data);
      } else {
        message.error(response.msg || '获取统计数据失败');
        setStatistics(null);
      }
    } catch (error: any) {
      console.error('获取统计数据失败:', error);
      message.error('获取统计数据失败');
      setStatistics(null);
    } finally {
      setStatisticsLoading(false);
    }
  }, []);

  return {
    // 数据状态
    data,
    statistics,

    // 加载状态
    loading,
    statisticsLoading,

    // 分页状态
    pagination,

    // 数据获取函数
    fetchData,
    fetchStatistics,

    // 状态更新函数
    setPagination,
  };
};
