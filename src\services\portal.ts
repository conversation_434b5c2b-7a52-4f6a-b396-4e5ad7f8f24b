import { request } from '@umijs/max';

// 门户公开服务

export interface PortalOverviewData {
  statistics: {
    mountain: number;
    waterSystem: number;
    historicalElement: number;
  };
  regionDistribution: Array<{
    region: string;
    regionId: number;
    mountainCount: number;
    waterSystemCount: number;
    historicalElementCount: number;
    total: number;
  }>;
  recentData: {
    mountains: Array<{ id: number; name: string; code: string; createdAt: string }>;
    waterSystems: Array<{ id: number; name: string; code: string; createdAt: string }>;
    historicalElements: Array<{ id: number; name: string; code: string; createdAt: string }>;
  };
}

export interface MapMarkerItem {
  id: number;
  type: 'mountain' | 'waterSystem' | 'historicalElement';
  name: string;
  longitude: number;
  latitude: number;
  thumbnailUrl?: string;
  summary?: string;
}

export async function getPortalOverview(params?: {
  regionId?: number;
}): Promise<API.ResType<PortalOverviewData>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/portal/overview${query.toString() ? `?${query.toString()}` : ''}`;
  return request(url, { method: 'GET' });
}

export async function getPortalMapMarkers(params?: {
  types?: Array<'mountain' | 'waterSystem' | 'historicalElement'>;
  regionId?: number;
}): Promise<API.ResType<MapMarkerItem[]>> {
  const query = new URLSearchParams();
  if (params?.types?.length) query.append('types', params.types.join(','));
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/portal/map-markers${query.toString() ? `?${query.toString()}` : ''}`;
  return request(url, { method: 'GET' });
}

export async function getStatisticsOverview(params?: {
  regionId?: number;
}): Promise<
  API.ResType<{
    counts: { mountain: number; waterSystem: number; historicalElement: number };
    regionStats: Array<{
      region: string;
      regionId: number;
      mountainCount: number;
      waterSystemCount: number;
      historicalElementCount: number;
      total: number;
    }>;
  }>
> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/overview${query.toString() ? `?${query.toString()}` : ''}`;
  return request(url, { method: 'GET' });
}

export async function getStatisticsTimeline(params?: {
  regionId?: number;
}): Promise<API.ResType<API.TimelineData[]>> {
  const query = new URLSearchParams();
  if (params?.regionId) query.append('regionId', String(params.regionId));
  const url = `/openapi/statistics/timeline${query.toString() ? `?${query.toString()}` : ''}`;
  return request(url, { method: 'GET' });
}

export async function getStatisticsGrowthTrend(params?: {
  period?: 'week' | 'month' | 'year';
}): Promise<
  API.ResType<{
    mountains: Array<{ date: string; count: number }>;
    waterSystems: Array<{ date: string; count: number }>;
    historicalElements: Array<{ date: string; count: number }>;
  }>
> {
  const query = new URLSearchParams();
  if (params?.period) query.append('period', params.period);
  const url = `/openapi/statistics/growth-trend${query.toString() ? `?${query.toString()}` : ''}`;
  return request(url, { method: 'GET' });
}

export async function openSearch(params: {
  keyword: string;
  type?: 'mountain' | 'waterSystem' | 'historicalElement' | 'all';
  page?: number;
  pageSize?: number;
}): Promise<
  API.ResType<{
    list: Array<
      | (API.Mountain & { type: 'mountain' })
      | (API.WaterSystem & { type: 'waterSystem' })
      | (API.HistoricalElement & { type: 'historicalElement' })
    >;
    total: number;
    page: number;
    pageSize: number;
  }>
> {
  const query = new URLSearchParams();
  query.append('keyword', params.keyword);
  if (params.type) query.append('type', params.type);
  if (params.page) query.append('page', String(params.page));
  if (params.pageSize) query.append('pageSize', String(params.pageSize));
  const url = `/openapi/search?${query.toString()}`;
  return request(url, { method: 'GET' });
}


