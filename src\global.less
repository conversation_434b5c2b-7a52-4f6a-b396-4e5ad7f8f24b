/* 全局样式 */
* {
  box-sizing: border-box;
}

html,
body {
  margin: 0;
  padding: 0;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC',
    'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial,
    sans-serif, 'Apple Color Emoji', 'Segoe UI Emoji', 'Segoe UI Symbol';
  background-color: #f5f5f5;
}

/* 公共网站样式 */
.public-layout {
  min-height: 100vh;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.public-header {
  background: rgba(255, 255, 255, 95%);
  backdrop-filter: blur(10px);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 10%);
  position: sticky;
  top: 0;
  z-index: 1000;
  padding: 0 24px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 64px;
}

.public-logo {
  display: flex;
  align-items: center;
  font-size: 20px;
  font-weight: bold;
  color: #1890ff;
}

.public-nav {
  display: flex;
  gap: 32px;
}

.public-nav-item {
  color: #333;
  text-decoration: none;
  font-weight: 500;
  padding: 8px 16px;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.3s;
}

.public-nav-item:hover,
.public-nav-item.active {
  background: #1890ff;
  color: white;
}

.admin-entry {
  padding: 8px 16px;
  background: #52c41a;
  color: white;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.3s;
}

.admin-entry:hover {
  background: #389e0d;
}

/* 内容区域 */
.public-content {
  padding: 0; /* 首页不需要默认padding */
  min-height: calc(100vh - 64px);
}

/* 通用容器 */
.container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 0 24px;
}

/* 卡片样式 */
.content-card {
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  overflow: hidden;
}

/* 地图容器 */
.map-container {
  width: 100%;
  height: 600px;
  border-radius: 8px;
  overflow: hidden;
}

/* 详情页样式 */
.detail-layout {
  display: grid;
  grid-template-columns: 1fr 1fr 300px;
  gap: 24px;
  margin-top: 24px;
}

.detail-info {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

.detail-images {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

.detail-relations {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
}

/* 列表页样式 */
.list-container {
  margin-top: 24px;
}

.list-item {
  background: white;
  border-radius: 12px;
  padding: 20px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  transition: all 0.3s;
  cursor: pointer;
}

.list-item:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 15%);
}

/* 增强卡片样式 */
.ant-card.ant-card-hoverable:hover {
  transform: translateY(-4px);
  box-shadow: 0 8px 24px rgba(0, 0, 0, 12%) !important;
}

.ant-card {
  transition: all 0.3s ease !important;
}

/* 列表页面卡片样式优化 */
.ant-list-item {
  padding: 0 !important;
  margin-bottom: 0 !important;
}

.ant-list-grid .ant-col > .ant-list-item {
  margin-bottom: 24px;
}

/* 分页样式优化 */
.ant-pagination {
  margin-top: 32px;
  text-align: center;
}

.ant-pagination .ant-pagination-item {
  border-radius: 6px;
}

.ant-pagination .ant-pagination-item-active {
  background: #1890ff;
  border-color: #1890ff;
}

.ant-pagination .ant-pagination-item-active a {
  color: white;
}

/* 按钮样式优化 */
.ant-btn {
  border-radius: 6px;
  font-weight: 500;
  transition: all 0.3s ease;
}

.ant-btn-primary {
  box-shadow: 0 2px 4px rgba(24, 144, 255, 0.3);
}

.ant-btn-primary:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(24, 144, 255, 0.4);
}

/* 输入框样式优化 */
.ant-input, .ant-input-affix-wrapper {
  border-radius: 6px;
  transition: all 0.3s ease;
}

.ant-input:focus, .ant-input-affix-wrapper:focus {
  box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
}

/* 标题样式优化 */
.ant-typography h1, .ant-typography h2, .ant-typography h3 {
  color: #333;
  font-weight: 600;
}

/* 图片样式优化 */
.ant-image {
  border-radius: 6px;
  overflow: hidden;
}

.ant-image-img {
  transition: all 0.3s ease;
}

.ant-image:hover .ant-image-img {
  transform: scale(1.02);
}

/* 详情页面响应式布局 */
@media (max-width: 1200px) {
  .detail-layout {
    grid-template-columns: 1fr 1fr;
  }
}

@media (max-width: 768px) {
  .detail-layout {
    grid-template-columns: 1fr;
  }
}

/* 数字化页面样式 */
.digital-filters {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: center;
}

.digital-charts {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

.chart-container {
  background: white;
  padding: 24px;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 10%);
  height: 400px;
}

/* 响应式设计 */
@media (max-width: 1200px) {
  .detail-layout {
    grid-template-columns: 1fr 1fr !important;
  }

  .content-card {
    margin: 16px !important;
  }
}

@media (max-width: 768px) {
  .public-header {
    flex-direction: column;
    height: auto;
    padding: 16px;
  }

  .public-nav {
    gap: 16px;
    margin-top: 16px;
  }

  .detail-layout {
    grid-template-columns: 1fr !important;
  }

  .digital-charts {
    grid-template-columns: 1fr;
  }

  .list-container {
    grid-template-columns: 1fr;
  }

  .content-card {
    margin: 12px !important;
    padding: 16px !important;
  }

  .ant-card {
    margin-bottom: 16px;
  }
}

/* 登录页面样式 */
.login-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.login-form {
  background: white;
  padding: 40px;
  border-radius: 12px;
  box-shadow: 0 8px 32px rgba(0, 0, 0, 10%);
  width: 400px;
  max-width: 90vw;
}

.login-title {
  text-align: center;
  font-size: 24px;
  font-weight: bold;
  margin-bottom: 32px;
  color: #333;
}

.login-logo {
  text-align: center;
  margin-bottom: 24px;
}

.login-logo img {
  width: 80px;
  height: 80px;
  margin-bottom: 16px;
  transition: transform 0.3s ease;
}

.login-logo img:hover {
  transform: scale(1.05);
}

/* 用户信息组件样式 */
.user-info-trigger:hover {
  background-color: rgba(0, 0, 0, 4%);
}

.user-dropdown-trigger:hover {
  background-color: rgba(0, 0, 0, 4%);
}
